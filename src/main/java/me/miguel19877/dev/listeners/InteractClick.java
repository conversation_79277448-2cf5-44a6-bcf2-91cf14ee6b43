package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.<PERSON>up;
import me.miguel19877.dev.managers.VIPManager;
import me.miguel19877.dev.utils.InventoryUtils;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.ParticleEffect;
import net.minecraft.server.v1_8_R3.*;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.block.Chest;
import org.bukkit.craftbukkit.v1_8_R3.entity.CraftPlayer;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.*;
import org.bukkit.entity.Entity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.weather.WeatherChangeEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.EulerAngle;
import org.bukkit.util.Vector;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

public class InteractClick implements Listener {



        @EventHandler
        public void onPlayerInteract(PlayerInteractEvent e) {
//            if (!LoginRegisterManager.isLoggedIn(e.getPlayer())) {
//                e.setCancelled(true);
//                return;
//            }
            if (e.getAction() == Action.RIGHT_CLICK_BLOCK || e.getAction() == Action.LEFT_CLICK_BLOCK) {
                Location location = e.getClickedBlock().getLocation();
                int x = location.getBlockX();
                int y = location.getBlockY();
                int z = location.getBlockZ();
                if (e.getItem() != null) {
                    if (e.getItem().getEnchantments().containsKey(Enchantment.DURABILITY) && e.getItem().getType() == Material.TRIPWIRE_HOOK) {
                        if (x == -55 && y == 31 && z == 41) {
                            if (!e.getItem().getItemMeta().getDisplayName().equalsIgnoreCase("§a§lCaixa Lapis")) {
                                e.setCancelled(true);
                                e.getPlayer().sendMessage("§cVocê precisa de uma §a§lChave Lápis §cpara abrir a caixa.");
                                return;
                            }
                            if (Rankup.caixas.containsValue("lapis")) {
                                e.getPlayer().sendMessage("§cAguarde o fim da abertura da caixa.");
                                e.setCancelled(true);
                                return;
                            }
                            String tipo = e.getItem().getItemMeta().getDisplayName().replaceAll("§a§lCaixa ", "");
                            e.setCancelled(true);
                            e.getPlayer().getInventory().remove(e.getItem());
                            Location location1 = e.getClickedBlock().getLocation();
                            caixalapis(tipo, e.getPlayer(), location1);
                        }else if (x == -55 && y == 31 && z == 44) {
                            if (!e.getItem().getItemMeta().getDisplayName().equalsIgnoreCase("§a§lCaixa Redstone")) {
                                e.setCancelled(true);
                                e.getPlayer().sendMessage("§cVocê precisa de uma §a§lChave Redstone §cpara abrir a caixa.");
                                return;
                            }
                            if (Rankup.caixas.containsValue("redstone")) {
                                e.getPlayer().sendMessage("§cAguarde o fim da abertura da caixa.");
                                e.setCancelled(true);
                                return;
                            }
                            String tipo = e.getItem().getItemMeta().getDisplayName().replaceAll("§a§lCaixa ", "");
                            e.setCancelled(true);
                            e.getPlayer().getInventory().remove(e.getItem());
                            Location location1 = e.getClickedBlock().getLocation();
                            caixaredstone(tipo, e.getPlayer(), location1);
                        }
                    }else {
                        if (x == -55 && y == 31 && z == 41) {
                            e.setCancelled(true);
                            e.getPlayer().sendMessage("§cVocê precisa de uma §a§lChave Lápis §cpara abrir a caixa.");
                        }
                        if (x == -55 && y == 31 && z == 44) {
                            e.setCancelled(true);
                            e.getPlayer().sendMessage("§cVocê precisa de uma §a§lChave Lápis §cpara abrir a caixa.");
                        }
                    }
                }else {
                    if (x == -55 && y == 31 && z == 41) {
                        e.setCancelled(true);
                        e.getPlayer().sendMessage("§cVocê precisa de uma §a§lChave Lápis §cpara abrir a caixa.");
                    }
                }
            }
            if (e.getAction() == Action.PHYSICAL) {
                if (e.getClickedBlock().getType() == Material.GOLD_PLATE) {
                    Player p = e.getPlayer();
                    if ((e.getClickedBlock().getLocation().getBlockZ() >= -104 && e.getClickedBlock().getLocation().getBlockZ() <= -65) && (e.getClickedBlock().getLocation().getBlockX() >= 799 && e.getClickedBlock().getLocation().getBlockX() <= 835)) {
                        p.removePotionEffect(PotionEffectType.SPEED);
                        p.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 200, 1));
                    }
                    if (e.getClickedBlock().getLocation().getBlockX() == 867 && e.getClickedBlock().getLocation().getBlockZ() == -84) {
                        p.teleport(p.getLocation().add(0, 1, 0));
                        Location l = p.getLocation();
                        l.setYaw(-90);
                        p.setVelocity(l.getDirection().normalize().multiply(25).setY(1.3));
                    }
                    if (e.getClickedBlock().getLocation().getBlockX() == 769 && e.getClickedBlock().getLocation().getBlockZ() == -84) {
                        p.teleport(p.getLocation().add(0, 1, 0));
                        Location l = p.getLocation();
                        l.setYaw(90);
                        p.setVelocity(l.getDirection().normalize().multiply(25).setY(1.3));
                    }
                    if (e.getClickedBlock().getLocation().getBlockX() == 818 && e.getClickedBlock().getLocation().getBlockZ() == -35) {
                        p.teleport(p.getLocation().add(0, 1, 0));
                        Location l = p.getLocation();
                        l.setYaw(0);
                        p.setVelocity(l.getDirection().normalize().multiply(25).setY(1.3));
                    }
                    if (e.getClickedBlock().getLocation().getBlockX() == 818 && e.getClickedBlock().getLocation().getBlockZ() == -133) {
                        p.teleport(p.getLocation().add(0, 1, 0));
                        Location l = p.getLocation();
                        l.setYaw(180);
                        p.setVelocity(l.getDirection().normalize().multiply(25).setY(1.3));
                    }
                }
                if (e.getClickedBlock().getType() == Material.WOOD_PLATE) {
                    Player p = e.getPlayer();
                    if ((e.getClickedBlock().getLocation().getBlockX() >= 606 && e.getClickedBlock().getLocation().getBlockX() <= 660) && (e.getClickedBlock().getLocation().getBlockZ() >= 24 && e.getClickedBlock().getLocation().getBlockZ() <= 78)) {
                        p.removePotionEffect(PotionEffectType.SPEED);
                        p.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 100, 1));
                    }
                }
            }
        }

        @EventHandler
        public void Weather(WeatherChangeEvent e) {
            if (e.toWeatherState() && e.getWorld().getEnvironment() == org.bukkit.World.Environment.NORMAL) {
                e.setCancelled(true);
            }
            e.setCancelled(e.toWeatherState());
        }


    //Caixa lapis method
    public void caixalapis(String tipo, Player p, Location location) {
        Rankup.caixas.put(p.getName(), "lapis");

        // Adjust the location for the lootbox display
        location.add(0.5, 0.5, 0.5);
        location.setYaw(180);

        // Spawn the initial armor stand
        ArmorStand armorStand = p.getWorld().spawn(location, ArmorStand.class);
        armorStand.setGravity(false);
        armorStand.setVisible(false);
        armorStand.setHelmet(new ItemStack(Material.CHEST));

        new BukkitRunnable() {
            Location initialLocation = armorStand.getLocation();
            Block chestBlock;
            boolean isSecondPhase = false;
            ArmorStand stand1, stand2, stand3;
            int countdown = 140;

            @Override
            public void run() {
                if (!isSecondPhase) {
                    // Animate the armor stand moving upwards and rotating
                    Location updatedLocation = armorStand.getLocation().clone().add(0, 0.04, 0);
                    updatedLocation.setYaw(updatedLocation.getYaw() + 9.5F);
                    armorStand.teleport(updatedLocation);

                    // Check if the armor stand has reached the desired height
                    if (updatedLocation.getY() > initialLocation.getY() + 2.00) {
                        spawnChestAndStands(updatedLocation);
                        armorStand.remove();
                        isSecondPhase = true;
                    }
                } else {
                    handleSecondPhase();
                }
            }

            private void spawnChestAndStands(Location chestLocation) {
                chestLocation.add(0, 1, 0);
                chestLocation.setYaw(-90);

                // Set the chest facing east
                chestLocation.getBlock().setType(Material.CHEST);
                chestBlock = chestLocation.getBlock();
                Chest chestState = (Chest) chestBlock.getState();
                chestState.setData(new org.bukkit.material.Chest(BlockFace.EAST)); // Chest faces east
                chestState.update();

                // Adjust positions for armor stands with holograms and keys
                Location armorStand1Location = chestLocation.clone().add(1.5, -1.4, 0);
                stand1 = spawnInvisibleArmorStand(armorStand1Location, new ItemStack(Material.TRIPWIRE_HOOK), new EulerAngle(Math.toRadians(90), Math.toRadians(180), Math.toRadians(0)));

                // Ensure the hologram text is positioned correctly
                Location armorStand2Location = armorStand1Location.clone().add(-1.5, 0,0); // Adjusted for better positioning
                stand2 = spawnInvisibleArmorStand(armorStand2Location, null, null);
                stand2.setCustomName("§aCaixa " + tipo); // Keep hardcoded for now as this is visual display
                stand2.setCustomNameVisible(true);
            }

            private void handleSecondPhase() {
                // Animate the movement of the first stand
                if (stand1.getLocation().getX() < initialLocation.getX() + 0.72) {
                    if (countdown == 138) {
                        openChest();
                    }
                    if (countdown == 130) {
                        openLootboxInventory();
                    }

                    countdown--;

                    if (countdown == 0) {
                        cleanup();
                        closeChest();
                    }
                } else {
                    moveArmorStand();
                }
            }

            private ArmorStand spawnInvisibleArmorStand(Location loc, ItemStack helmet, EulerAngle headPose) {
                ArmorStand stand = p.getWorld().spawn(loc, ArmorStand.class);
                stand.setGravity(false);
                stand.setVisible(false);
                if (helmet != null) stand.setHelmet(helmet);
                if (headPose != null) stand.setHeadPose(headPose);
                return stand;
            }

            private void openChest() {
                BlockPosition blockPosition = new BlockPosition(chestBlock.getX(), chestBlock.getY(), chestBlock.getZ());
                PacketPlayOutBlockAction packet = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 1, 1);
                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packet);
                p.playSound(p.getLocation(), Sound.CHEST_OPEN, 1, 1);
            }

            private void closeChest() {
                BlockPosition blockPosition = new BlockPosition(chestBlock.getX(), chestBlock.getY(), chestBlock.getZ());
                PacketPlayOutBlockAction packet = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 0, 0);
                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packet);
                p.playSound(p.getLocation(), Sound.CHEST_CLOSE, 1, 1);
            }

            private void openLootboxInventory() {
                Inventory inv = Bukkit.createInventory(null, 27, "§1Caixa " + tipo);
                p.openInventory(inv);
                new BukkitRunnable() {
                    int ticks = 0;

                    @Override
                    public void run() {
                        updateLootboxInventory(inv);
                        ticks++;
                        if (ticks == 120) {
                            // Set all items to glass panes except 13
                            for (int i = 0; i < 27; i++) {
                                if (i != 13) inv.setItem(i, new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 15));
                            }
                            // Add delay before closing the inventory and giving the reward
                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    finalizeLootbox(inv);
                                }
                            }.runTaskLater(Rankup.getInstance(), 40L); // 20 ticks = 1 second delay

                            cancel();
                        }
                    }
                }.runTaskTimer(Rankup.getInstance(), 0, 2);
            }

            private void updateLootboxInventory(Inventory inv) {
                // Update inventory with glass panes and shifting rewards
                int randomColor = (int) (Math.random() * 14);
                ItemStack pane = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) randomColor);

                for (int i = 0; i < 9; i++) inv.setItem(i, pane);
                for (int i = 18; i < 27; i++) inv.setItem(i, pane);

                // Shift items in the middle row and assign new rewards
                for (int i = 9; i < 17; i++) {
                    inv.setItem(i, inv.getItem(i + 1));
                }
                assignRandomReward(inv, 17);
            }

            private void assignRandomReward(Inventory inv, int slot) {
                int random = (int) (Math.random() * 100);
                ItemStack reward = null;
                if (random <= 49) {
                    reward = createItemStack(Material.DOUBLE_PLANT, "§6Sem Prémio");
                } else if (random <= 59) {
                    reward = createItemStack(Material.DIAMOND_SPADE, "§aPá Random");
                } else if (random <= 69) {
                    reward = createItemStack(Material.DIAMOND_AXE, "§aMachado Random");
                } else if (random <= 77) {
                    reward = createItemStack(Material.EXP_BOTTLE, "§6Poção de XP", 16);
                } else if (random <= 84) {
                    reward = createItemStack(Material.DIAMOND, "§6Diamante", 32);
                } else if (random <= 96) {
                    reward = createItemStack(Material.EMERALD, "§6Dinheiro");
                } else if (random <= 99) {
                    reward = createItemStack(Material.GOLDEN_APPLE, "§6Maçã Dourada", 16, (short) 1);
                }
                inv.setItem(slot, reward);
            }

            private ItemStack createItemStack(Material material, String name) {
                return createItemStack(material, name, 1);
            }

            private ItemStack createItemStack(Material material, String name, int amount) {
                return createItemStack(material, name, amount, (short) 0);
            }

            private ItemStack createItemStack(Material material, String name, int amount, short damage) {
                ItemStack item = new ItemStack(material, amount, damage);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(name);
                item.setItemMeta(meta);
                return item;
            }

            private void finalizeLootbox(Inventory inv) {
                ItemStack selectedItem = inv.getItem(13);
                if (selectedItem == null || selectedItem.getItemMeta() == null) return;

                String rewardName = selectedItem.getItemMeta().getDisplayName();
                giveRewardToPlayer(rewardName);
                inv.clear();
                p.closeInventory();
            }

            private void giveRewardToPlayer(String rewardName) {
                switch (rewardName) {
                    case "§aPá Random":
                        giveRandomEnchantedItem(Material.DIAMOND_SPADE, "§aPá Random");
                        break;
                    case "§aMachado Random":
                        giveRandomEnchantedItem(Material.DIAMOND_AXE, "§aMachado Random");
                        break;
                    case "§6Poção de XP":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.EXP_BOTTLE, 16));
                        break;
                    case "§6Diamante":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.DIAMOND, 32));
                        break;
                    case "§6Dinheiro":
                        Economy.addMoney(p.getUniqueId(), 1000L);
                        break;
                    case "§6Maçã Dourada":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.GOLDEN_APPLE, 16, (short) 1));
                        break;
                    default:
                        p.sendMessage("§cVocê não ganhou nada.");
                }
            }

            private void giveRandomEnchantedItem(Material material, String name) {
                ItemStack item = new ItemStack(material, 1);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(name);
                List<Enchantment> applicableEnchantments = getApplicableEnchantments(material);
                Enchantment randomEnchantment = applicableEnchantments.get(new Random().nextInt(applicableEnchantments.size()));
                item.setItemMeta(meta);
                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                p.getInventory().addItem(item);
            }

            private void cleanup() {
                chestBlock.setType(Material.AIR);
                Rankup.caixas.remove(p.getName());
                stand1.remove();
                stand2.remove();
                stand3.remove();
                this.cancel();
            }

            private void moveArmorStand() {
                Location loc = stand1.getLocation().clone().add(-0.04, 0, 0);
                stand1.teleport(loc);
            }
        }.runTaskTimer(Rankup.getInstance(), 0, 1);
    }

    public void caixaredstone(String tipo, Player p, Location location) {
        Rankup.caixas.put(p.getName(), "redstone");

        // Adjust the location for the lootbox display
        location.add(0.5, 0.5, 0.5);
        location.setYaw(180);

        // Spawn the initial armor stand
        ArmorStand armorStand = p.getWorld().spawn(location, ArmorStand.class);
        armorStand.setGravity(false);
        armorStand.setVisible(false);
        armorStand.setHelmet(new ItemStack(Material.CHEST));

        new BukkitRunnable() {
            Location initialLocation = armorStand.getLocation();
            Block chestBlock;
            boolean isSecondPhase = false;
            ArmorStand stand1, stand2, stand3;
            int countdown = 140;

            @Override
            public void run() {
                if (!isSecondPhase) {
                    // Animate the armor stand moving upwards and rotating
                    Location updatedLocation = armorStand.getLocation().clone().add(0, 0.04, 0);
                    updatedLocation.setYaw(updatedLocation.getYaw() + 9.5F);
                    armorStand.teleport(updatedLocation);

                    // Check if the armor stand has reached the desired height
                    if (updatedLocation.getY() > initialLocation.getY() + 2.00) {
                        spawnChestAndStands(updatedLocation);
                        armorStand.remove();
                        isSecondPhase = true;
                    }
                } else {
                    handleSecondPhase();
                }
            }

            private void spawnChestAndStands(Location chestLocation) {
                chestLocation.add(0, 1, 0);
                chestLocation.setYaw(-90);

                // Set the chest facing east
                chestLocation.getBlock().setType(Material.CHEST);
                chestBlock = chestLocation.getBlock();
                Chest chestState = (Chest) chestBlock.getState();
                chestState.setData(new org.bukkit.material.Chest(BlockFace.EAST)); // Chest faces east
                chestState.update();

                // Adjust positions for armor stands with holograms and keys
                Location armorStand1Location = chestLocation.clone().add(1.5, -1.4, 0);
                stand1 = spawnInvisibleArmorStand(armorStand1Location, new ItemStack(Material.TRIPWIRE_HOOK), new EulerAngle(Math.toRadians(90), Math.toRadians(180), Math.toRadians(0)));

                // Ensure the hologram text is positioned correctly
                Location armorStand2Location = armorStand1Location.clone().add(-1.5, 0,0); // Adjusted for better positioning
                stand2 = spawnInvisibleArmorStand(armorStand2Location, null, null);
                stand2.setCustomName("§aCaixa " + tipo); // Keep hardcoded for now as this is visual display
                stand2.setCustomNameVisible(true);
            }

            private void handleSecondPhase() {
                // Animate the movement of the first stand
                if (stand1.getLocation().getX() < initialLocation.getX() + 0.72) {
                    if (countdown == 138) {
                        openChest();
                    }
                    if (countdown == 130) {
                        openLootboxInventory();
                    }

                    countdown--;

                    if (countdown == 0) {
                        cleanup();
                        closeChest();
                    }
                } else {
                    moveArmorStand();
                }
            }

            private ArmorStand spawnInvisibleArmorStand(Location loc, ItemStack helmet, EulerAngle headPose) {
                ArmorStand stand = p.getWorld().spawn(loc, ArmorStand.class);
                stand.setGravity(false);
                stand.setVisible(false);
                if (helmet != null) stand.setHelmet(helmet);
                if (headPose != null) stand.setHeadPose(headPose);
                return stand;
            }

            private void openChest() {
                BlockPosition blockPosition = new BlockPosition(chestBlock.getX(), chestBlock.getY(), chestBlock.getZ());
                PacketPlayOutBlockAction packet = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 1, 1);
                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packet);
                p.playSound(p.getLocation(), Sound.CHEST_OPEN, 1, 1);
            }

            private void closeChest() {
                BlockPosition blockPosition = new BlockPosition(chestBlock.getX(), chestBlock.getY(), chestBlock.getZ());
                PacketPlayOutBlockAction packet = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 0, 0);
                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packet);
                p.playSound(p.getLocation(), Sound.CHEST_CLOSE, 1, 1);
            }

            private void openLootboxInventory() {
                Inventory inv = Bukkit.createInventory(null, 27, "§cCaixa " + tipo);
                p.openInventory(inv);
                new BukkitRunnable() {
                    int ticks = 0;

                    @Override
                    public void run() {
                        updateLootboxInventory(inv);
                        ticks++;
                        if (ticks == 120) {
                            // Set all items to glass panes except 13
                            for (int i = 0; i < 27; i++) {
                                if (i != 13) inv.setItem(i, new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 15));
                            }
                            // Add delay before closing the inventory and giving the reward
                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    finalizeLootbox(inv);
                                }
                            }.runTaskLater(Rankup.getInstance(), 40L); // 20 ticks = 1 second delay

                            cancel();
                        }
                    }
                }.runTaskTimer(Rankup.getInstance(), 0, 2);
            }

            private void updateLootboxInventory(Inventory inv) {
                // Update inventory with glass panes and shifting rewards
                int randomColor = (int) (Math.random() * 14);
                ItemStack pane = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) randomColor);

                for (int i = 0; i < 9; i++) inv.setItem(i, pane);
                for (int i = 18; i < 27; i++) inv.setItem(i, pane);

                // Shift items in the middle row and assign new rewards
                for (int i = 9; i < 17; i++) {
                    inv.setItem(i, inv.getItem(i + 1));
                }
                assignRandomReward(inv, 17);
            }

            private void assignRandomReward(Inventory inv, int slot) {
                int random = (int) (Math.random() * 100);
                ItemStack reward = null;
                if (random <= 49) {
                    //reward = createItemStack(Material.DOUBLE_PLANT, "§6Sem Prémio");
                    reward = createItemStack(Material.PAPER, "§6VIP 15 Minutos", 1);
                } else if (random <= 59) {
                    reward = createItemStack(Material.DIAMOND_SPADE, "§aPá Random");
                } else if (random <= 65) {
                    reward = createItemStack(Material.DIAMOND_AXE, "§aMachado Random");
                } else if (random <= 73) {
                    reward = createItemStack(Material.EXP_BOTTLE, "§6Poção de XP", 16);
                } else if (random <= 81) {
                    reward = createItemStack(Material.DIAMOND, "§6Diamante", 32);
                } else if (random <= 91) {
                    reward = createItemStack(Material.EMERALD, "§6Dinheiro");
                } else if (random <= 94) {
                    reward = createItemStack(Material.GOLDEN_APPLE, "§6Maçã Dourada", 16, (short) 1);
                }else if (random <= 96) {
                    reward = createItemStack(Material.PAPER, "§6VIP 15 Minutos", 1);
                }else if (random <= 99) {
                    reward = createItemStack(Material.DIAMOND_CHESTPLATE, "§6Armadura Random", 1);
                }
                inv.setItem(slot, reward);
            }

            private ItemStack createItemStack(Material material, String name) {
                return createItemStack(material, name, 1);
            }

            private ItemStack createItemStack(Material material, String name, int amount) {
                return createItemStack(material, name, amount, (short) 0);
            }

            private ItemStack createItemStack(Material material, String name, int amount, short damage) {
                ItemStack item = new ItemStack(material, amount, damage);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(name);
                item.setItemMeta(meta);
                return item;
            }

            private void finalizeLootbox(Inventory inv) {
                ItemStack selectedItem = inv.getItem(13);
                if (selectedItem == null || selectedItem.getItemMeta() == null) return;

                String rewardName = selectedItem.getItemMeta().getDisplayName();
                giveRewardToPlayer(rewardName);
                inv.clear();
                p.closeInventory();
            }

            private void giveRewardToPlayer(String rewardName) {
                switch (rewardName) {
                    case "§aPá Random":
                        giveRandomEnchantedItem(Material.DIAMOND_SPADE, "§aPá Random");
                        break;
                    case "§aMachado Random":
                        giveRandomEnchantedItem(Material.DIAMOND_AXE, "§aMachado Random");
                        break;
                    case "§6Poção de XP":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.EXP_BOTTLE, 16));
                        break;
                    case "§6Diamante":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.DIAMOND, 32));
                        break;
                    case "§6Dinheiro":
                        Economy.addMoney(p.getUniqueId(), 1000L);
                        break;
                    case "§6Maçã Dourada":
                        InventoryUtils.safeAddItem(p, new ItemStack(Material.GOLDEN_APPLE, 16, (short) 1));
                        break;
                    case "§6VIP 15 Minutos":
                        p.sendMessage("§aVocê ganhou VIP por 15 minutos.");
                        //get current system milliseconds then add 15 minutes
                        long time = System.currentTimeMillis() + 900000;
                        VIPManager.setTime(p.getUniqueId(), time);
                        ativarVIP("vip", p);
                        break;
                    case "§6Armadura Random":
                        //random chance of any piece of diamond armor. 1/4 chance of each piece
                        int random = (int) (Math.random() * 4);
                        switch (random) {
                            case 0:
                                giveRandomProtectionEnchantedItem(Material.DIAMOND_HELMET, "§aArmadura Random");
                                break;
                            case 1:
                                giveRandomProtectionEnchantedItem(Material.DIAMOND_CHESTPLATE, "§aArmadura Random");
                                break;
                            case 2:
                                giveRandomProtectionEnchantedItem(Material.DIAMOND_LEGGINGS, "§aArmadura Random");
                                break;
                            case 3:
                                giveRandomProtectionEnchantedItem(Material.DIAMOND_BOOTS, "§aArmadura Random");
                                break;
                        }
                    default:
                        p.sendMessage("§cVocê não ganhou nada.");
                }
            }

            private void giveRandomEnchantedItem(Material material, String name) {
                ItemStack item = new ItemStack(material, 1);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(name);
                List<Enchantment> applicableEnchantments = getApplicableEnchantments(material);
                Enchantment randomEnchantment = applicableEnchantments.get(new Random().nextInt(applicableEnchantments.size()));
                item.setItemMeta(meta);
                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                p.getInventory().addItem(item);
            }

            private void giveRandomProtectionEnchantedItem(Material material, String name) {
                ItemStack item = new ItemStack(material, 1);
                ItemMeta meta = item.getItemMeta();
                meta.setDisplayName(name);
                item.setItemMeta(meta);
                // Random protection between 2 and 8
                item.addUnsafeEnchantment(Enchantment.PROTECTION_ENVIRONMENTAL, new Random().nextInt(6) + 2);
                p.getInventory().addItem(item);
            }

            private void cleanup() {
                chestBlock.setType(Material.AIR);
                Rankup.caixas.remove(p.getName());
                stand1.remove();
                stand2.remove();
                stand3.remove();
                this.cancel();
            }

            private void moveArmorStand() {
                Location loc = stand1.getLocation().clone().add(-0.04, 0, 0);
                stand1.teleport(loc);
            }
        }.runTaskTimer(Rankup.getInstance(), 0, 1);
    }

    public void caixaouro(String tipo, Player p, Location location) {
            Rankup.caixas.put(p.getName(), "ouro");
            location.add(0.5, 0.5 , 0.5);
            location.setYaw(180);
            ArmorStand armorStand = p.getWorld().spawn(location, ArmorStand.class);
            armorStand.setGravity(false);
            armorStand.setVisible(false);
            armorStand.setHelmet(new ItemStack(Material.CHEST));

            new BukkitRunnable() {
                Location sameLocation = armorStand.getLocation();
                Block chestLocation;
                boolean second = false;
                ArmorStand armorStand1;
                ArmorStand armorStand2;
                ArmorStand armorStand3;
                int countdown = 140;
                @Override
                public void run() {
                    if (!second) {
                        Location location = armorStand.getLocation();
                        location.add(0, 0.04, 0);
                        location.setYaw((location.getYaw() + 9.5F));

                        armorStand.teleport(location);

                        if (armorStand.getLocation().getY() > (2.00 + sameLocation.getY())) {
                            location.add(0, 1,0);
                            location.setYaw(-90);
                            location.getBlock().setType(Material.CHEST);
                            chestLocation = location.getBlock();
                            sameLocation = armorStand.getLocation();
                            armorStand.remove();
                            second = true;
                            location.add(0, -1.4,-1.5);
                            location.setYaw(-90);
                            armorStand1 = p.getWorld().spawn(location, ArmorStand.class);
                            armorStand1.setGravity(false);
                            armorStand1.setVisible(false);
                            armorStand1.setHelmet(new ItemStack(Material.TRIPWIRE_HOOK));
                            armorStand1.setHeadPose(new EulerAngle(Math.toRadians(90), Math.toRadians(180), Math.toRadians(0)));
                            location.add(-0.4, 0.7, 2.1);
                            armorStand2 = p.getWorld().spawn(location, ArmorStand.class);
                            armorStand2.setGravity(false);
                            armorStand2.setVisible(false);
                            armorStand2.setCustomNameVisible(false);
                            armorStand2.setCustomName("§aCaixa " + tipo);
                            armorStand2.setArms(true);
                            armorStand2.setRightArmPose(new EulerAngle(Math.toRadians(270), Math.toRadians(0), Math.toRadians(0)));
                        }
                    }else {

                        if (armorStand1.getLocation().getZ() > (sameLocation.getZ()) - 0.72) {
                            if (countdown == 138) {
                                location.add(0, 2.0, 0);
                                armorStand1.setHeadPose(new EulerAngle(Math.toRadians(90), Math.toRadians(180), Math.toRadians(90)));
                                BlockPosition blockPosition = new BlockPosition(chestLocation.getX(), chestLocation.getY(), chestLocation.getZ());
                                PacketPlayOutBlockAction packetPlayOutBlockAction = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 1, 1);
                                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packetPlayOutBlockAction);
                                p.playSound(p.getLocation(), Sound.CHEST_OPEN, 1, 1);
                                armorStand3 = p.getWorld().spawn(location, ArmorStand.class);
                                armorStand3.setGravity(false);
                                armorStand3.setVisible(false);
                                armorStand3.setCustomNameVisible(true);
                                armorStand3.setCustomName("§aCaixa " + tipo);

                            }
                            if (countdown == 130) {
                                Inventory inv = Bukkit.createInventory(null, 27, "§6Caixa Ouro");

                                p.openInventory(inv);
                                new BukkitRunnable() {
                                    int i = 0;
                                    @Override
                                    public void run() {
                                        //random int for the color of the glass
                                        int random2 = (int) (Math.random() * 14);
                                        for (int i = 0; i < 9; i++) {
                                            ItemStack item = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) random2);
                                            inv.setItem(i, item);
                                        }
                                        for (int i = 18; i < 27; i++) {
                                            ItemStack item = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) random2);
                                            inv.setItem(i, item);
                                        }
                                        i++;
                                        if (i < 100) {
                                            for (int i = 9; i < 17; i++) {
                                                inv.setItem(i, inv.getItem(i + 1));
                                            }
                                            int random = (int) (Math.random() * 100);
                                            if (random <= 49) {
                                                ItemStack item = new ItemStack(Material.DOUBLE_PLANT, 1, (short) 0 );
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Sem Prémio");
                                                item.setItemMeta(meta);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 50 && random <= 69) {
                                                ItemStack item = new ItemStack(Material.DIAMOND_SPADE, 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§aPá Random");
                                                List<Enchantment> applicableEnchantments = getApplicableEnchantments(Material.DIAMOND_SPADE);
                                                Enchantment randomEnchantment = applicableEnchantments.get((int) (Math.random() * applicableEnchantments.size()));
                                                item.setItemMeta(meta);
                                                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 70 && random <= 79) {
                                                ItemStack item = new ItemStack(Material.DIAMOND_AXE, 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§aMachado Random");
                                                List<Enchantment> applicableEnchantments = getApplicableEnchantments(Material.DIAMOND_AXE);
                                                Enchantment randomEnchantment = applicableEnchantments.get((int) (Math.random() * applicableEnchantments.size()));
                                                item.setItemMeta(meta);
                                                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 80 && random <= 87) {
                                                //random for 16 or 32
                                                int random16or32 = (int) (Math.random() * 2);
                                                ItemStack item = new ItemStack(Material.EXP_BOTTLE, random16or32 == 0 ? 16 : 32);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Poção de XP");
                                                item.setItemMeta(meta);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 88 && random <= 94) {
                                                ItemStack item = new ItemStack(Material.DIAMOND, 32);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Diamante");
                                                item.setItemMeta(meta);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 95 && random <= 96) {
                                                ItemStack item = new ItemStack(Material.EMERALD, 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Dinheiro");
                                                item.setItemMeta(meta);
                                                inv.setItem(17, item);
                                            }
                                            if (random >= 97 && random <= 99) {
                                                ItemStack item = new ItemStack(Material.GOLDEN_APPLE, 16 , (short) 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Maçã Dourada");
                                                item.setItemMeta(meta);
                                                inv.setItem(17, item);
                                            }

                                        }else if (i == 120) {
                                            cancel();
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§6Sem Prémio")) {
                                                p.sendMessage("§cVocê não ganhou nada.");
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§aPá Random")) {
                                                ItemStack item = new ItemStack(Material.DIAMOND_SPADE, 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§aPá Random");
                                                List<Enchantment> applicableEnchantments = getApplicableEnchantments(Material.DIAMOND_SPADE);
                                                Enchantment randomEnchantment = applicableEnchantments.get((int) (Math.random() * applicableEnchantments.size()));
                                                item.setItemMeta(meta);
                                                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                                                p.getInventory().addItem(item);
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§aMachado Random")) {
                                                ItemStack item = new ItemStack(Material.DIAMOND_AXE, 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§aMachado Random");
                                                List<Enchantment> applicableEnchantments = getApplicableEnchantments(Material.DIAMOND_AXE);
                                                Enchantment randomEnchantment = applicableEnchantments.get((int) (Math.random() * applicableEnchantments.size()));
                                                item.setItemMeta(meta);
                                                item.addUnsafeEnchantment(randomEnchantment, new Random().nextInt(3) + 1);
                                                p.getInventory().addItem(item);
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§6Poção de XP")) {
                                                ItemStack item = new ItemStack(Material.EXP_BOTTLE, 16);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Poção de XP");
                                                item.setItemMeta(meta);
                                                p.getInventory().addItem(item);
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§6Diamante")) {
                                                ItemStack item = new ItemStack(Material.DIAMOND, 32);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Diamante");
                                                item.setItemMeta(meta);
                                                p.getInventory().addItem(item);
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§6Dinheiro")) {
                                                Economy.addMoney(p.getUniqueId(), 1000L);
                                            }
                                            if (inv.getItem(13).getItemMeta().getDisplayName().equalsIgnoreCase("§6Maçã Dourada")) {
                                                ItemStack item = new ItemStack(Material.GOLDEN_APPLE, 16 , (short) 1);
                                                ItemMeta meta = item.getItemMeta();
                                                meta.setDisplayName("§6Maçã Dourada");
                                                item.setItemMeta(meta);
                                                p.getInventory().addItem(item);
                                            }

                                            inv.clear();
                                            p.closeInventory();

                                        }else {
                                            for (int i = 0; i < 27; i++) {
                                                if (i != 13) {
                                                    ItemStack item = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) random2);
                                                    inv.setItem(i, item);
                                                }
                                            }
                                        }
                                    }
                                }.runTaskTimer(Rankup.getInstance(), 0, 2);
                            }

                            countdown--;
                            if (countdown == 10) {
                                BlockPosition blockPosition = new BlockPosition(chestLocation.getX(), chestLocation.getY(), chestLocation.getZ());
                                PacketPlayOutBlockAction packetPlayOutBlockAction = new PacketPlayOutBlockAction(blockPosition, Blocks.CHEST, 0, 0);
                                ((CraftPlayer) p).getHandle().playerConnection.sendPacket(packetPlayOutBlockAction);
                                p.playSound(p.getLocation(), Sound.CHEST_CLOSE, 1, 1);
                            }
                            if (countdown == 0) {
                                chestLocation.setType(Material.AIR);
                                sameLocation = armorStand1.getLocation();

                                Rankup.caixas.remove(p.getName());



                                armorStand1.remove();
                                armorStand2.remove();
                                armorStand3.remove();
                                this.cancel();
                            }
                        }else {
                            Location location = armorStand1.getLocation();
                            location.add(0, 0, 0.04);
                            armorStand1.teleport(location);
                        }
                    }


                }
            }.runTaskTimer(Rankup.getInstance(), 0, 1);
        }

    public void caixaGiantEgg(Player player, Location location) {
        // Add player to active opening list
        Rankup.caixas.put(player.getName(), "obsidiana");

        // Remove the chest that was right-clicked
        Block chestBlock = location.getBlock();
        chestBlock.setType(Material.AIR);

        // Spawn an invisible giant holding an egg
        Location giantLocation = location.clone().add(2.5, -8, -3.5);
        Giant giant = (Giant) location.getWorld().spawnEntity(giantLocation, EntityType.GIANT);
        giant.addPotionEffect(new PotionEffect(PotionEffectType.INVISIBILITY, Integer.MAX_VALUE, 1, false, false));
        giant.getEquipment().setItemInHand(new ItemStack(Material.EGG, 1));
        // Create an egg hatching animation in a 5x5 radius
        new BukkitRunnable() {
            int ticks = 0;

            @Override
            public void run() {
                if (ticks >= 100) {
                    // After 3 seconds (60 ticks), display the reward
                    displayReward(player, location);
                    giant.remove();
                    chestBlock.setType(Material.CHEST);
                    Rankup.caixas.remove(player.getName());
                    this.cancel();
                    return;
                }
                if (ticks == 60) {
                    ItemStack diamond = new ItemStack(Material.DIAMOND, 1);
                    giant.getEquipment().setItemInHand(diamond);
                }
                // Animation: egg hatching particles in a 5x5 radius
                if (ticks <= 60 && ticks % 15 == 0 && ticks > 1) {
                    for (int x = -2; x <= 2; x++) {
                        for (int z = -2; z <= 2; z++) {
                            Location particleLocation = location.clone().add(x + 0.5, 1, z + 0.5);
                            ParticleEffect.EXPLOSION_HUGE.display(0, 0, 0, 0, 1, particleLocation, 100);
                        }
                    }
                }

                ticks++;
            }
        }.runTaskTimer(Rankup.getInstance(), 0, 1);
    }

    private void displayReward(Player player, Location location) {
        // Placeholder for reward display logic
        // Replace this with actual reward logic
        player.sendMessage("§aRecebeste uma recompensa");

        // Example reward: give the player a diamond
        ItemStack reward = new ItemStack(Material.DIAMOND);
        player.getInventory().addItem(reward);
        player.updateInventory();
    }

    public List<Enchantment> getApplicableEnchantments(Material item) {
        List<Enchantment> applicable = new ArrayList<>();

        applicable.add(Enchantment.DIG_SPEED);
        applicable.add(Enchantment.DURABILITY);
        applicable.add(Enchantment.LOOT_BONUS_BLOCKS);
        applicable.add(Enchantment.SILK_TOUCH);
        if (item.equals(Material.DIAMOND_AXE)) {
            applicable.add(Enchantment.DAMAGE_ALL);
            applicable.add(Enchantment.DAMAGE_ARTHROPODS);
            applicable.add(Enchantment.DAMAGE_UNDEAD);
            applicable.add(Enchantment.FIRE_ASPECT);
            applicable.add(Enchantment.KNOCKBACK);
        }

        return applicable;

    }


    private boolean ativarVIP(String vipType, Player targetPlayer) {
        if (Permissions.vipExists(vipType)) {
            Permissions.setGrupo(targetPlayer, Permissions.convertVipToId(vipType));
            targetPlayer.kickPlayer("§r§aVIP ativado com sucesso!§r\n§r§bFoste kickado para as permissoes ficarem atualizadas§r");
            return true;
        } else {
            return false;
        }
    }
}
