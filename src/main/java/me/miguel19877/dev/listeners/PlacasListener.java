package me.miguel19877.dev.listeners;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.Rankup;
import me.miguel19877.dev.managers.AchievementShopManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.managers.PrestigeManager;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.InventoryUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.block.Sign;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.SignChangeEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class PlacasListener implements Listener {

    // Cooldown map to prevent rapid clicking exploitation
    private static final Map<UUID, Long> playerCooldowns = new ConcurrentHashMap<>();
    private static final long COOLDOWN_MS = 500; // 500ms cooldown between clicks

    // Static block to start cleanup task
    static {
        // Clean up old cooldown entries every 30 seconds to prevent memory leaks
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();
                playerCooldowns.entrySet().removeIf(entry ->
                    (currentTime - entry.getValue()) > 30000); // Remove entries older than 30 seconds
            }
        }.runTaskTimerAsynchronously(Rankup.getInstance(), 600L, 600L); // 30 seconds = 600 ticks
    }

    @EventHandler
    public void onPlaca(SignChangeEvent e) {
        Player p = e.getPlayer();
        if (Permissions.getGrupoId(p) >= 13) {
            if (e.getLine(0).equalsIgnoreCase("[VENDER]")) {
                e.setLine(0, "§4[VENDER]");
                e.setLine(1, "§b§l" + e.getLine(1));
                e.setLine(2, "§b§l" + e.getLine(2) + "€");
                e.setLine(3, "§b§l" + e.getLine(3));
            }
            if (e.getLine(0).equalsIgnoreCase("[COMPRAR]")) {
                e.setLine(0, "§2[COMPRAR]");
                e.setLine(1, "§b§l" + e.getLine(1));
                e.setLine(2, "§b§l" + e.getLine(2) + "€");
                e.setLine(3, "§b§l" + e.getLine(3));
            }
            if (e.getLine(0).equalsIgnoreCase("VENDER/COMPRAR")) {
                e.setLine(0, "§2VENDER/COMPRAR");
                e.setLine(1, "§b§l" + e.getLine(1));
                e.setLine(2, "§b§l" + e.getLine(2) + "€");
                e.setLine(3, "§b§l" + e.getLine(3));
            }
            if (e.getLine(0).equalsIgnoreCase("[LIVRO]")) {
                if (Enchantment.getByName(ChatColor.stripColor(e.getLine(1))) == null && getEnchantment(ChatColor.stripColor(e.getLine(1))) == null){
                    LanguageManager.getInstance().sendMessage(p, "placas.enchantment_not_found");
                    return;
                }
                e.setLine(0, "§2[LIVRO]");
                e.setLine(1, "§b§l" + e.getLine(1));
                e.setLine(2, "§b§l" + e.getLine(2));
                e.setLine(3, "§b§l" + e.getLine(3) + "€");
            }
        }
    }

    @EventHandler
    public void interacao(PlayerInteractEvent e) {
        if (e.getAction() == Action.RIGHT_CLICK_BLOCK) {
            if (e.getClickedBlock() != null) {
                if (e.getClickedBlock().getType() == Material.WALL_SIGN || e.getClickedBlock().getType() == Material.SIGN_POST) {
                    Sign sign = (Sign) e.getClickedBlock().getState();
                    Player p = e.getPlayer();

                    // Check cooldown to prevent rapid clicking exploitation
                    UUID playerId = p.getUniqueId();
                    long currentTime = System.currentTimeMillis();
                    Long lastClick = playerCooldowns.get(playerId);

                    if (lastClick != null && (currentTime - lastClick) < COOLDOWN_MS) {
                        // Player is clicking too fast, ignore this click
                        return;
                    }

                    // Update cooldown
                    playerCooldowns.put(playerId, currentTime);

                    if (sign.getLine(0).equalsIgnoreCase("§4[VENDER]")) {
                        String stripped3 = ChatColor.stripColor(sign.getLine(1));
                        String stripped = ChatColor.stripColor(sign.getLine(2));
                        String stripped2 = ChatColor.stripColor(sign.getLine(3));
                        long price = Long.parseLong(stripped.replace("€", ""));
                        //if it contains : try to split
                        ItemStack itemToSell = getItemStackFromString(stripped2);
                        Material material = itemToSell.getType();

                        // Synchronized block to prevent race conditions
                        synchronized (p.getInventory()) {
                            // Re-calculate inventory in synchronized block to ensure accuracy
                            long quantityInInventory = 0;
                            for (ItemStack item : p.getInventory().getContents()) {
                                if (item != null) {
                                    if (item.getType() == itemToSell.getType() && item.getData().getData() == itemToSell.getData().getData()) {
                                        quantityInInventory += item.getAmount();
                                    }
                                }
                            }

                            if (quantityInInventory > 0) {
                                double prestigeMultiplier = 1.0;
                                if (p.getWorld().getName().equals("minas")) {
                                    int prestigeLevel = PrestigeManager.getPrestigeLevel(p);
                                    prestigeMultiplier += 0.05 * prestigeLevel; // 5% per prestige level
                                }

                                // The money to give is the quantity of the item in the inventory times the price of the item,
                                // then divided by the quantity of the item in the sign, and then multiplied by the prestige multiplier
                                long moneyToGive = Math.round(
                                        ((quantityInInventory * price) / (double) Long.parseLong(stripped3)) * prestigeMultiplier
                                );

                                // Remove items atomically - remove all matching items at once
                                int removedCount = 0;
                                for (int i = 0; i < p.getInventory().getSize(); i++) {
                                    ItemStack item = p.getInventory().getItem(i);
                                    if (item != null && item.getType() == itemToSell.getType() &&
                                        item.getData().getData() == itemToSell.getData().getData()) {
                                        removedCount += item.getAmount();
                                        p.getInventory().setItem(i, null);
                                    }
                                }

                                p.updateInventory();
                                p.sendMessage("§aVendeste " + removedCount + " " +
                                        material.name().substring(0, 1).toUpperCase() +
                                        material.name().substring(1).toLowerCase() +
                                        " por " + moneyToGive + "€.");
                                Economy.addMoney(p.getUniqueId(), moneyToGive);

                                // Track achievement progress for selling
                                AchievementShopManager.getInstance().onPlayerSell(p, removedCount, moneyToGive);
                            } else {
                                p.sendMessage("§cNão tens §f" +
                                        material.name().substring(0, 1).toUpperCase() +
                                        material.name().substring(1).toLowerCase() +
                                        "§c para vender.");
                            }
                        } // End synchronized block
                    }
                    if (sign.getLine(0).equalsIgnoreCase("§2[COMPRAR]")) {
                        String stripped3 = ChatColor.stripColor(sign.getLine(1));
                        String stripped = ChatColor.stripColor(sign.getLine(2));
                        String stripped2 = ChatColor.stripColor(sign.getLine(3));
                        long price = Long.parseLong(stripped.replace("€", ""));
                        ItemStack itemToBuy = getItemStackFromString(stripped2);
                        Material material = itemToBuy.getType();
                        long money = Economy.getMoney(p.getUniqueId());
                        //check for inventory space by the 36 slots
                        int space = 0;
                        for (ItemStack item : p.getInventory().getContents()) {
                            if (item == null) {
                                space++;
                            }
                        }
                        if (money >= price) {
                            if (space > 0) {
                                int quantity = Integer.parseInt(stripped3);
                                if (quantity > 0) {
                                    Economy.removeMoney(p.getUniqueId(), (long) price);
                                    itemToBuy.setAmount(quantity);
                                    boolean addedToInventory = InventoryUtils.safeAddItem(p, itemToBuy);
                                    p.updateInventory();
                                    if (addedToInventory) {
                                        p.sendMessage("§aCompraste " + quantity + " " + material.name().substring(0, 1).toUpperCase() + material.name().substring(1).toLowerCase() + " por " + price + "€.");
                                    } else {
                                        p.sendMessage("§aCompraste " + quantity + " " + material.name().substring(0, 1).toUpperCase() + material.name().substring(1).toLowerCase() + " por " + price + "€. §eItens foram dropados no chão!");
                                    }
                                } else {
                                    p.sendMessage("§cNão tens espaço no inventário para comprar " + material.name().substring(0, 1).toUpperCase() + material.name().substring(1).toLowerCase() + ".");
                                }
                            } else {
                                p.sendMessage("§cNão tens espaço no inventário para comprar " + material.name().substring(0, 1).toUpperCase() + material.name().substring(1).toLowerCase() + ".");
                            }
                        } else {
                            p.sendMessage("§cNão tens dinheiro suficiente para comprar " + material.name().substring(0, 1).toUpperCase() + material.name().substring(1).toLowerCase() + ".");
                        }
                    }
                    if (sign.getLine(0).equalsIgnoreCase("§2[LIVRO]")) {
                        String stripped3 = ChatColor.stripColor(sign.getLine(1));
                        String stripped = ChatColor.stripColor(sign.getLine(2));
                        String stripped2 = ChatColor.stripColor(sign.getLine(3));
                        long price = Long.parseLong(stripped2.replace("€", ""));
                        ItemStack itemToBuy = new ItemStack(Material.ENCHANTED_BOOK, 1);
                        Enchantment enchantment = Enchantment.getByName(stripped3);
                        if (enchantment == null) {
                            enchantment = getEnchantment(stripped3);
                        }
                        int nivel = Integer.parseInt(stripped);
                        itemToBuy.addUnsafeEnchantment(enchantment, nivel);
                        long money = Economy.getMoney(p.getUniqueId());
                        //check for inventory space by the 36 slots
                        int space = 0;
                        for (ItemStack item : p.getInventory().getContents()) {
                            if (item == null) {
                                space++;
                            }
                        }
                        if (money >= price) {
                            if (space > 0) {
                                int quantity = 1;
                                Economy.removeMoney(p.getUniqueId(), (long) price);
                                itemToBuy.setAmount(quantity);
                                p.getInventory().addItem(itemToBuy);
                                p.updateInventory();
                                p.sendMessage("§aCompraste um livro com o encantamento " + stripped3 + " por " + price + "€.");
                            } else {
                                p.sendMessage("§cNão tens espaço no inventário para comprar livro com o encantamento " + stripped3 + ".");
                            }
                        } else {
                            p.sendMessage("§cNão tens dinheiro suficiente para comprar livro com o encantamento " + stripped3 + ".");
                        }
                    }
                }
            }
        }
    }

    private ItemStack getItemStackFromString(String signString) {
        if (signString.contains(":")) {
            String[] split = signString.split(":");
            int id = Integer.parseInt(split[0]);
            int data = Integer.parseInt(split[1]);
            Material material = Material.getMaterial(id);
            return new ItemStack(material, 1, (short) data);
        } else {
            int id = Integer.parseInt(signString);
            Material material = Material.getMaterial(id);
            return new ItemStack(material);
        }
    }

    private static Enchantment getEnchantment(String enchString) {
        // Clean up string - make lowercase and strip space/dash/underscore
        enchString = enchString.toLowerCase().replaceAll("[ _-]", "");

        // Set up aliases (this could probably be done outside the function so
        // we only do it once (eg. in a support class init or read from a file)
        Map<String, String> aliases = new HashMap<>();
        aliases.put("aspectfire", "fireaspect");
        aliases.put("sharpness", "damageall");
        aliases.put("smite", "damageundead");
        aliases.put("punch", "arrowknockback");
        aliases.put("looting", "lootbonusmobs");
        aliases.put("fortune", "lootbonusblocks");
        aliases.put("baneofarthropods", "damageundead");
        aliases.put("power", "arrowdamage");
        aliases.put("flame", "arrowfire");
        aliases.put("infinity", "arrowinfinite");
        aliases.put("unbreaking", "durability");
        aliases.put("efficiency", "digspeed");
        aliases.put("smite", "damageundead");

        // If an alias exists, use it
        String alias = aliases.get(enchString);
        if (alias != null)
            enchString = alias;

        // Loop through all enchantments and match (case insensitive and ignoring space,
        // underscore and dashes
        for (Enchantment value : Enchantment.values()) {
            if (enchString.equalsIgnoreCase(value.getName().replaceAll("[ _-]", ""))) {
                return value;
            }
        }

        return null; // nothing found.
    }

}
