package me.miguel19877.dev.listeners;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import me.miguel19877.dev.Rankup;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.messaging.PluginMessageListener;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class TopListener implements PluginMessageListener {

    int[] slots = {13, 21, 23, 29, 33};
    private final Gson gson = new Gson();

    @Override
    public void onPluginMessageReceived(String channel, Player player, byte[] message) {
        if (!channel.equals("cah:topfive")) {
            return;
        }

        try {
            String json = new String(message, StandardCharsets.UTF_8);
            Type mapType = new TypeToken<Map<String, Object>>(){}.getType();
            Map<String, Object> data = gson.fromJson(json, mapType);

            // Parse the top lists
            List<Map<String, Object>> topMoney = (List<Map<String, Object>>) data.get("topMoney");
            List<Map<String, Object>> topKills = (List<Map<String, Object>>) data.get("topKills");
            List<Map<String, Object>> topBlocksMined = (List<Map<String, Object>>) data.get("topBlocksMined");

            // Update inventories on the main thread
            Bukkit.getScheduler().runTask(Rankup.getInstance(), () -> {
                if (topMoney != null) {
                    updateTopMoneyInventory(Rankup.topMoneyInventory, topMoney);
                }
                if (topKills != null) {
                    updateTopKillsInventory(Rankup.topKillsInventory, topKills);
                }
                if (topBlocksMined != null) {
                    updateTopBlocksMinedInventory(Rankup.topBlocksMinedInventory, topBlocksMined);
                }
            });

        } catch (Exception e) {
            Bukkit.getLogger().severe("Error processing top five message: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Updates the provided inventory with the top 5 money earners.
     * Each entry is represented by a player head with their name and money amount.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topMoneyInventory The inventory to update.
     * @param topMoney A list of Map objects containing position, username, and value fields.
     */
    public void updateTopMoneyInventory(Inventory topMoneyInventory, List<Map<String, Object>> topMoney) {
        for (int i = 0; i < slots.length; i++) {
            // Create a new player head item.
            // The data value (short) 3 is used for player heads.
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a player for the current rank.
            if (i < topMoney.size()) {
                Map<String, Object> playerData = topMoney.get(i);
                String username = (String) playerData.get("username");
                Number value = (Number) playerData.get("value");

                // Set the owner of the skull to the player's name.
                skullMeta.setOwner(username);
                // Set the display name of the item.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + username);
                // Set the lore (description) of the item with the player's money.
                skullMeta.setLore(Collections.singletonList("§a§l" + CustomTabScoreboard.convert(value.longValue()) + "€")); // Format money.
            } else {
                // If no player for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topMoneyInventory.setItem(slots[i], skull);
        }
    }

    /**
     * Updates the provided inventory with the top 5 players by kills.
     * Each entry is represented by a player head with their name and kill count.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topKillsInventory The inventory to update.
     * @param topKills A list of Map objects containing position, username, and value fields.
     */
    public void updateTopKillsInventory(Inventory topKillsInventory, List<Map<String, Object>> topKills) {
        for (int i = 0; i < slots.length; i++) {
            // Create a new player head item.
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a player for the current rank.
            if (i < topKills.size()) {
                Map<String, Object> playerData = topKills.get(i);
                String username = (String) playerData.get("username");
                Number value = (Number) playerData.get("value");

                // Set the owner of the skull to the player's name.
                skullMeta.setOwner(username);
                // Set the display name of the item.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + username);
                // Set the lore (description) of the item with the player's kill count.
                skullMeta.setLore(Collections.singletonList("§c§l" + value.intValue() + " Kills")); // Cast score to int for kills.
            } else {
                // If no player for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A Kills"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topKillsInventory.setItem(slots[i], skull);
        }
    }

    /**
     * Updates the provided inventory with the top 5 players by blocks mined.
     * Each entry is represented by a player head with their name and blocks mined count.
     * If there are fewer than 5 entries, the remaining slots are filled with "N/A" placeholders.
     *
     * @param topBlocksMinedInventory The inventory to update.
     * @param topBlocksMined A list of Map objects containing position, username, and value fields.
     */
    public void updateTopBlocksMinedInventory(Inventory topBlocksMinedInventory, List<Map<String, Object>> topBlocksMined) {
        for (int i = 0; i < slots.length; i++) {
            // Create a new player head item.
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            // Get the SkullMeta to modify the player head's properties.
            SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

            // Check if there is a player for the current rank.
            if (i < topBlocksMined.size()) {
                Map<String, Object> playerData = topBlocksMined.get(i);
                String username = (String) playerData.get("username");
                Number value = (Number) playerData.get("value");

                // Set the owner of the skull to the player's name.
                skullMeta.setOwner(username);
                // Set the display name of the item.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - " + username);
                // Set the lore (description) of the item with the blocks mined count.
                skullMeta.setLore(Collections.singletonList("§a§l" + CustomTabScoreboard.convert(value.longValue()) + " Blocos")); // Format blocks mined.
            } else {
                // If no player for this rank, set a question mark head.
                skullMeta.setOwner("MHF_Question");
                // Set display name to N/A.
                skullMeta.setDisplayName("§6§lTop " + (i + 1) + " - N/A");
                // Set lore to N/A.
                skullMeta.setLore(Collections.singletonList("§c§lN/A Blocos"));
            }
            // Apply the modified SkullMeta back to the ItemStack.
            skull.setItemMeta(skullMeta);
            // Place the skull item in the designated slot in the inventory.
            topBlocksMinedInventory.setItem(slots[i], skull);
        }
    }

}
