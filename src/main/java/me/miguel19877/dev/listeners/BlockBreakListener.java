package me.miguel19877.dev.listeners;

import me.miguel19877.dev.<PERSON>up;
import me.miguel19877.dev.Tasks.Minas;
import me.miguel19877.dev.managers.BlocksMinedManager;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import me.miguel19877.dev.utils.Cuboid;
import me.miguel19877.dev.utils.TitleAPI;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.ExperienceOrb;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockFadeEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.block.LeavesDecayEvent;
import org.bukkit.event.entity.FoodLevelChangeEvent;
import org.bukkit.event.player.PlayerBucketEmptyEvent;
import org.bukkit.event.player.PlayerBucketFillEvent;
import org.bukkit.event.server.ServerListPingEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.material.Leaves;

import java.util.ArrayList;
import java.util.Collection;

public class BlockBreakListener implements Listener {

    @EventHandler
    public void leafFade(BlockFadeEvent e) {
        if (e.getBlock().getType() == Material.LEAVES || e.getBlock().getType() == Material.LEAVES_2) {
            e.setCancelled(true);
        }
    }

    @EventHandler
    public void leafFade2(LeavesDecayEvent e) {
        e.setCancelled(true);
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        if (!event.getPlayer().getWorld().getName().equals("plotworld")) {
            if (Permissions.getGrupoId(event.getPlayer()) < 12) {
                event.setCancelled(true);
            }
        }
        if (event.getPlayer().getWorld().getName().equals("minas")) {
            Collection<Cuboid> minas = Rankup.minas.values();
            int x = event.getBlock().getLocation().getBlockX();
            int y = event.getBlock().getLocation().getBlockY();
            int z = event.getBlock().getLocation().getBlockZ();
            boolean yes = false;
            for (Cuboid mina : minas) {
                if (mina.contains(x, y, z)) {
                    yes = true;
                    break;
                }
            }
            if (yes) {
                //Set the block to air, cancel the event. The drops will go directly to the player's inventory.
                event.setCancelled(true); // Cancel the event to prevent default block drop and allow custom handling
                Collection<ItemStack> drops = event.getBlock().getDrops();
                for (ItemStack drop : drops) {
                    //XP to drop, between 2-5 and increase by 1 for each level of the enchantment, spawn xp orbs
                    World w = event.getBlock().getWorld();
                    ExperienceOrb orb = w.spawn(event.getBlock().getLocation(), ExperienceOrb.class);
                    int xp = 1;
                    if (checkBlockIsOre(event.getBlock())) {
                        if (event.getPlayer().getItemInHand().getEnchantmentLevel(Enchantment.LOOT_BONUS_BLOCKS) > 0) {
                            xp += (int) Math.round(Math.random() * event.getPlayer().getItemInHand().getEnchantmentLevel(Enchantment.LOOT_BONUS_BLOCKS));
                            drop.setAmount(drop.getAmount() + (int) Math.round(Math.random() * event.getPlayer().getItemInHand().getEnchantmentLevel(Enchantment.LOOT_BONUS_BLOCKS)));
                        }
                    }
                    orb.setExperience(xp);
                    event.getPlayer().getInventory().addItem(drop);
                    event.getPlayer().updateInventory();
                }
                event.getBlock().setType(Material.AIR); // Set the block to air after handling drops
                event.getPlayer().getItemInHand().setDurability((short) (event.getPlayer().getItemInHand().getDurability() + 1));
                if ((event.getPlayer().getItemInHand().getDurability()) >= event.getPlayer().getItemInHand().getType().getMaxDurability()) {
                    event.getPlayer().getInventory().setItemInHand(new ItemStack(Material.AIR));
                }
                event.getPlayer().updateInventory();
                BlocksMinedManager.addBlocksMined(event.getPlayer(), 1);
            } else {
                int rankid = Permissions.getGrupoId(event.getPlayer());
                if (rankid < 12) {
                    event.setCancelled(true);
                }
            }
        }
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        if (event.getPlayer().getWorld().getName().equals("minas")) {
            if (Permissions.getGrupoId(event.getPlayer()) < 12) {
                event.setCancelled(true);
            }
        }
        if (!event.getPlayer().getWorld().getName().equals("plotworld")) {
            if (Permissions.getGrupoId(event.getPlayer()) < 12) {
                event.setCancelled(true);
            }
        }
    }


    @EventHandler
    public void onFood(FoodLevelChangeEvent e) {
        e.setFoodLevel(20);
        if (e.getEntity() instanceof Player) {
            Player p = (Player) e.getEntity();
            p.setSaturation(20);
        }
    }

    @EventHandler
    public void baldes(PlayerBucketFillEvent e) {
        if (!e.getPlayer().getWorld().getName().equals("plotworld")) {
            e.setCancelled(true);
            e.getPlayer().updateInventory();
        }
    }

    @EventHandler
    public void baldes2(PlayerBucketEmptyEvent e) {
        if (!e.getPlayer().getWorld().getName().equals("plotworld")) {
            e.setCancelled(true);
            e.getPlayer().updateInventory();
        }
    }

    @EventHandler
    public void onMotd(ServerListPingEvent event) {
        // MOTD is server-wide and doesn't need per-player language support
        event.setMotd("§l   §6                 CraftAndHelps §7[1.8]§r\n§l §b§l        Reabertura Oficial §8| §4Rankup 1.0");
    }

    public boolean checkBlockIsOre(Block block) {
        ArrayList<Integer> ores = new ArrayList<>();
        ores.add(14);
        ores.add(15);
        ores.add(16);
        ores.add(21);
        ores.add(56);
        ores.add(73);
        ores.add(129);
        ores.add(153);

        return ores.contains(Integer.valueOf(block.getType().getId()));
    }


}
