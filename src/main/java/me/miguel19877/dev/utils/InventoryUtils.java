package me.miguel19877.dev.utils;

import me.miguel19877.dev.managers.LanguageManager;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.PlayerInventory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Utility class for handling inventory operations and warnings.
 */
public class InventoryUtils {
    private static final Map<UUID, Long> lastWarningTime = new HashMap<>();
    private static final long WARNING_COOLDOWN_MS = 3_000L;

    /**
     * Safely adds an item to the player's inventory.
     * If the inventory is full, shows a warning and drops the item on the ground.
     *
     * @param player the player to give the item to
     * @param item   the item to add
     * @return true if the entire stack was added; false if anything was dropped
     */
    public static boolean safeAddItem(Player player, ItemStack item) {
        if (item == null || item.getAmount() <= 0) return true;

        // clone so we don't mutate the original (e.g. the hand stack!)
        ItemStack toAdd = item.clone();
        Map<Integer, ItemStack> leftover = player.getInventory().addItem(toAdd);

        if (!leftover.isEmpty()) {
            showInventoryFullWarning(player);
            leftover.values().forEach(drop ->
                    player.getWorld().dropItemNaturally(player.getLocation(), drop)
            );
            return false;
        }

        return true;
    }

    /**
     * Safely adds multiple items to the player's inventory.
     * Shows one warning if any of them overflow.
     *
     * @param player the player to give the items to
     * @param items  the items to add
     * @return the number of ItemStacks fully added (others are dropped)
     */
    public static int safeAddItems(Player player, ItemStack... items) {
        int addedCount = 0;
        boolean warned = false;

        for (ItemStack item : items) {
            if (item == null || item.getAmount() <= 0) continue;

            ItemStack toAdd = item.clone();
            Map<Integer, ItemStack> leftover = player.getInventory().addItem(toAdd);

            if (leftover.isEmpty()) {
                addedCount++;
            } else {
                if (!warned) {
                    showInventoryFullWarning(player);
                    warned = true;
                }
                leftover.values().forEach(drop ->
                        player.getWorld().dropItemNaturally(player.getLocation(), drop)
                );
            }
        }

        return addedCount;
    }

    /**
     * Checks whether the player's inventory has room for the entire given stack.
     *
     * @param player the player
     * @param item   the stack to check
     * @return true if all of it could fit, false otherwise
     */
    public static boolean hasInventorySpace(Player player, ItemStack item) {
        if (item == null || item.getAmount() <= 0) return true;

        int remaining = item.getAmount();
        PlayerInventory inv = player.getInventory();

        // slots 0–35 are the main inventory + hotbar
        for (int i = 0; i < 36; i++) {
            ItemStack slot = inv.getItem(i);
            if (slot == null) {
                // one empty slot can fit up to item.getMaxStackSize(), and
                // item.getAmount() will never exceed that in a valid ItemStack
                return true;
            }
            if (slot.isSimilar(item)) {
                int space = slot.getMaxStackSize() - slot.getAmount();
                remaining -= space;
                if (remaining <= 0) return true;
            }
        }

        return false;
    }

    /**
     * Shows an “inventory full” title+actionbar warning with a cooldown to avoid spam.
     */
    public static void showInventoryFullWarning(Player player) {
        UUID id = player.getUniqueId();
        long now = System.currentTimeMillis();
        Long last = lastWarningTime.get(id);
        if (last != null && now - last < WARNING_COOLDOWN_MS) return;

        lastWarningTime.put(id, now);
        LanguageManager lang = LanguageManager.getInstance();

        String title     = lang.getMessage(player, "inventory.full.title");
        String subtitle  = lang.getMessage(player, "inventory.full.subtitle");
        String actionBar = lang.getMessage(player, "inventory.full.actionbar");

        if (title    == null) title    = "&c&lInventário Cheio!";
        if (subtitle == null) subtitle = "&eItens foram dropados no chão";
        if (actionBar== null) actionBar= "&c⚠ &fInventário cheio! Limpe espaço para " +
                "continuar minerando &c⚠";

        TitleAPI.sendTitle(player, 10, 40, 10, title, subtitle);
        TitleAPI.sendOutChat(player, actionBar);
        player.playSound(player.getLocation(), Sound.NOTE_BASS, 1.0f, 0.5f);
    }

    /**
     * Periodically call this to purge old warning timestamps and
     * avoid a memory leak.
     */
    public static void cleanupOldWarnings() {
        long now = System.currentTimeMillis();
        lastWarningTime.entrySet().removeIf(e ->
                now - e.getValue() > WARNING_COOLDOWN_MS * 10
        );
    }
}