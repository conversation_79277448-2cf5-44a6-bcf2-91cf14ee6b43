package me.miguel19877.dev.utils.hologram;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerArmorStandManipulateEvent;
import org.bukkit.event.world.ChunkLoadEvent;
import org.bukkit.event.world.ChunkUnloadEvent;
import org.bukkit.event.world.WorldUnloadEvent;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Listener to protect hologram armor stands from player interactions.
 * Prevents damage, manipulation, handles cleanup when armor stands die,
 * and provides error recovery for chunk/world events.
 */
public class HologramListener implements Listener {

    private final HologramManager hologramManager;

    // Storage for hologram recovery data
    private final Map<String, HologramRecoveryData> recoveryData;

    // Recreation cooldown to prevent spam (hologram ID -> last recreation time)
    private final Map<String, Long> recreationCooldowns = new ConcurrentHashMap<>();
    private static final long RECREATION_COOLDOWN_MS = 5000; // 5 seconds cooldown

    /**
     * Creates a new HologramListener instance.
     *
     * @param hologramManager The hologram manager instance
     */
    public HologramListener(HologramManager hologramManager) {
        this.hologramManager = hologramManager;
        this.recoveryData = new HashMap<>();
    }

    /**
     * Data class to store hologram information for recovery purposes.
     */
    private static class HologramRecoveryData {
        final String id;
        final Location location;
        final String text;
        final List<String> lines;
        final double heightOffset;
        final boolean visible;
        final boolean isMultiLine;
        final long lastSeen;

        HologramRecoveryData(Hologram hologram) {
            this.id = hologram.getId();
            this.location = hologram.getLocation().clone();
            this.text = hologram.getCurrentText();
            this.lines = new ArrayList<>(hologram.getCurrentLines());
            this.heightOffset = hologram.getHeightOffset();
            this.visible = hologram.isVisible();
            this.isMultiLine = hologram.isMultiLine();
            this.lastSeen = System.currentTimeMillis();
        }
    }

    /**
     * Prevents players from damaging hologram armor stands.
     *
     * @param event The entity damage event
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onEntityDamageByEntity(EntityDamageByEntityEvent event) {
        // Check if the damaged entity is an armor stand
        if (!(event.getEntity() instanceof ArmorStand)) {
            return;
        }

        ArmorStand armorStand = (ArmorStand) event.getEntity();

        // Check if this armor stand belongs to a hologram
        if (isHologramArmorStand(armorStand)) {
            // Cancel the damage event to protect the hologram
            event.setCancelled(true);
        }
    }

    /**
     * Prevents players from manipulating hologram armor stands (placing/removing items).
     *
     * @param event The armor stand manipulation event
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerArmorStandManipulate(PlayerArmorStandManipulateEvent event) {
        ArmorStand armorStand = event.getRightClicked();

        // Check if this armor stand belongs to a hologram
        if (isHologramArmorStand(armorStand)) {
            // Cancel the manipulation event to protect the hologram
            event.setCancelled(true);
        }
    }

    /**
     * Handles cleanup when armor stands die (for example, due to explosions or other causes).
     *
     * @param event The entity death event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onEntityDeath(EntityDeathEvent event) {
        Entity entity = event.getEntity();

        // Check if the dead entity is an armor stand
        if (!(entity instanceof ArmorStand)) {
            return;
        }

        ArmorStand armorStand = (ArmorStand) entity;

        // Check if this armor stand belongs to a hologram
        if (isHologramArmorStand(armorStand)) {
            // Find and clean up the hologram reference
            cleanupDeadHologram(armorStand);
        }
    }

    /**
     * Checks if an armor stand belongs to a hologram by comparing it with all registered holograms.
     *
     * @param armorStand The armor stand to check
     * @return true if the armor stand belongs to a hologram, false otherwise
     */
    private boolean isHologramArmorStand(ArmorStand armorStand) {
        // Iterate through all registered holograms to find a match
        for (Hologram hologram : hologramManager.getAllHolograms().values()) {
            if (hologram.isValid() && hologram.getArmorStand() != null) {
                if (hologram.getArmorStand().equals(armorStand)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Cleans up hologram references when the underlying armor stand dies.
     * Stores recovery data and attempts to recreate the hologram.
     *
     * @param deadArmorStand The armor stand that died
     */
    private void cleanupDeadHologram(ArmorStand deadArmorStand) {
        // Find the hologram that owns this armor stand
        for (Hologram hologram : hologramManager.getAllHolograms().values()) {
            if (hologram.getArmorStand() != null && hologram.getArmorStand().equals(deadArmorStand)) {
                // Store recovery data before the hologram becomes invalid
                storeRecoveryData(hologram);

                // DO NOT attempt immediate recreation to prevent spam
                // Let the chunk load event or maintenance task handle recreation
                // This prevents infinite recreation loops
                break;
            }
        }
    }

    /**
     * Handles chunk unload events to properly clean up holograms in that chunk.
     *
     * @param event The chunk unload event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onChunkUnload(ChunkUnloadEvent event) {
        int chunkX = event.getChunk().getX();
        int chunkZ = event.getChunk().getZ();
        String worldName = event.getWorld().getName();

        // Find holograms in this chunk and store recovery data
        List<String> toStore = new ArrayList<>();
        for (Hologram hologram : hologramManager.getAllHolograms().values()) {
            if (hologram.getLocation().getWorld().getName().equals(worldName) &&
                    (hologram.getLocation().getBlockX() >> 4) == chunkX &&
                    (hologram.getLocation().getBlockZ() >> 4) == chunkZ) {

                // Only store recovery data if we don't already have it
                if (!recoveryData.containsKey(hologram.getId())) {
                    storeRecoveryData(hologram);
                }
                toStore.add(hologram.getId());
            }
        }

        // Force cleanup any remaining ArmorStands in this chunk to prevent multiplication
        cleanupArmorStandsInChunk(event.getChunk());

        // Remove holograms from manager but keep recovery data
        for (String hologramId : toStore) {
            hologramManager.removeHologram(hologramId);
        }
    }

    /**
     * Handles chunk load events to attempt hologram recreation.
     *
     * @param event The chunk load event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onChunkLoad(ChunkLoadEvent event) {
        int chunkX = event.getChunk().getX();
        int chunkZ = event.getChunk().getZ();
        String worldName = event.getWorld().getName();

        // Check if we have any recovery data for holograms in this chunk
        List<String> toRecreate = new ArrayList<>();
        for (HologramRecoveryData data : recoveryData.values()) {
            if (data.location.getWorld().getName().equals(worldName) &&
                    (data.location.getBlockX() >> 4) == chunkX &&
                    (data.location.getBlockZ() >> 4) == chunkZ) {

                // Only recreate if hologram doesn't already exist
                if (!hologramManager.hasHologram(data.id)) {
                    toRecreate.add(data.id);
                }
            }
        }

        // Attempt to recreate holograms with a small delay to ensure chunk is fully loaded
        if (!toRecreate.isEmpty()) {
            Bukkit.getScheduler().runTaskLater(hologramManager.getPlugin(), () -> {
                for (String hologramId : toRecreate) {
                    // Double-check that hologram doesn't exist before recreation
                    if (!hologramManager.hasHologram(hologramId)) {
                        attemptHologramRecreation(hologramId);
                    }
                }
            }, 5L); // 5 tick delay
        }
    }

    /**
     * Handles world unload events to clean up holograms in that world.
     *
     * @param event The world unload event
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onWorldUnload(WorldUnloadEvent event) {
        String worldName = event.getWorld().getName();

        // Store recovery data for all holograms in the unloading world
        List<String> toRemove = new ArrayList<>();
        for (Hologram hologram : hologramManager.getAllHolograms().values()) {
            if (hologram.getLocation().getWorld().getName().equals(worldName)) {
                storeRecoveryData(hologram);
                toRemove.add(hologram.getId());
            }
        }

        // Remove holograms from the manager (they'll be recreated when world loads)
        for (String hologramId : toRemove) {
            hologramManager.removeHologram(hologramId);
        }
    }

    /**
     * Stores recovery data for a hologram.
     *
     * @param hologram The hologram to store recovery data for
     */
    private void storeRecoveryData(Hologram hologram) {
        if (hologram != null && hologram.isValid()) {
            recoveryData.put(hologram.getId(), new HologramRecoveryData(hologram));
        }
    }

    /**
     * Attempts to recreate a hologram using stored recovery data.
     *
     * @param hologramId The ID of the hologram to recreate
     * @return true if recreation was successful, false otherwise
     */
    private boolean attemptHologramRecreation(String hologramId) {
        HologramRecoveryData data = recoveryData.get(hologramId);
        if (data == null) {
            return false;
        }

        try {
            // Check if the hologram already exists and is valid
            if (hologramManager.isHologramValid(hologramId)) {
                // Hologram already exists and is valid, remove recovery data
                recoveryData.remove(hologramId);
                recreationCooldowns.remove(hologramId);
                return true;
            }

            // Check recreation cooldown to prevent spam
            Long lastRecreation = recreationCooldowns.get(hologramId);
            long currentTime = System.currentTimeMillis();
            if (lastRecreation != null && (currentTime - lastRecreation) < RECREATION_COOLDOWN_MS) {
                return false; // Still in cooldown period
            }

            // Check if the location is valid for recreation
            if (data.location.getWorld() == null ||
                    !data.location.getWorld().isChunkLoaded(data.location.getBlockX() >> 4, data.location.getBlockZ() >> 4)) {
                return false; // Can't recreate yet, chunk not loaded
            }

            // Remove the existing invalid hologram if it exists
            hologramManager.removeHologram(hologramId);

            // Recreate the hologram
            Hologram newHologram;
            if (data.isMultiLine && !data.lines.isEmpty()) {
                // Recreate multi-line hologram
                newHologram = new Hologram(data.id, data.location, data.lines, data.heightOffset, data.visible);
            } else {
                // Recreate single-line hologram
                newHologram = new Hologram(data.id, data.location, data.text, data.heightOffset, data.visible);
            }

            // Add to manager using the proper method
            if (!hologramManager.addHologramToRegistry(newHologram)) {
                throw new RuntimeException("Failed to add hologram to registry");
            }

            // Remove recovery data since recreation was successful
            recoveryData.remove(hologramId);

            // Update cooldown to prevent immediate recreation
            recreationCooldowns.put(hologramId, System.currentTimeMillis());

            return true;

        } catch (Exception e) {
            // Recreation failed, keep recovery data for future attempts
            System.err.println("Failed to recreate hologram '" + hologramId + "': " + e.getMessage());
            return false;
        }
    }

    /**
     * Gets the number of holograms with stored recovery data.
     *
     * @return The count of holograms with recovery data
     */
    public int getRecoveryDataCount() {
        return recoveryData.size();
    }

    /**
     * Clears old recovery data that hasn't been used for recreation.
     * This should be called periodically to prevent memory leaks.
     *
     * @param maxAgeMillis Maximum age of recovery data in milliseconds
     */
    public void cleanupOldRecoveryData(long maxAgeMillis) {
        long currentTime = System.currentTimeMillis();

        // Clean up old recovery data
        recoveryData.entrySet().removeIf(entry ->
                (currentTime - entry.getValue().lastSeen) > maxAgeMillis);

        // Clean up old recreation cooldowns
        recreationCooldowns.entrySet().removeIf(entry ->
                (currentTime - entry.getValue()) > RECREATION_COOLDOWN_MS * 2); // Keep cooldowns for 2x the cooldown period
    }

    /**
     * Force cleanup any hologram ArmorStands in a specific chunk.
     * This prevents multiplication when chunks unload/load.
     *
     * @param chunk The chunk to clean up
     */
    private void cleanupArmorStandsInChunk(org.bukkit.Chunk chunk) {
        try {
            for (Entity entity : chunk.getEntities()) {
                if (entity instanceof ArmorStand) {
                    ArmorStand stand = (ArmorStand) entity;
                    // Check if this is a hologram ArmorStand (invisible, no gravity, has custom name)
                    if (!stand.isVisible() && !stand.hasGravity() &&
                        (stand.getCustomName() != null || stand.isCustomNameVisible())) {

                        // This looks like a hologram ArmorStand, remove it
                        stand.remove();
                    }
                }
            }
        } catch (Exception e) {
            // Ignore errors during cleanup to prevent crashes
            System.err.println("Error during chunk ArmorStand cleanup: " + e.getMessage());
        }
    }
}