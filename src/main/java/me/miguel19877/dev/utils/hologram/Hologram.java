package me.miguel19877.dev.utils.hologram;

import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Represents a hologram using an armor stand entity for displaying floating text.
 * Provides a simplified interface for managing armor stand properties and text updates.
 */
public class Hologram {

    private final String id;
    private final Location location;
    private ArmorStand armorStand; // For single-line compatibility
    private List<ArmorStand> armorStands; // For multi-line support
    private String currentText;
    private List<String> currentLines; // For multi-line text storage
    private boolean visible;
    private double heightOffset;
    private static final double LINE_SPACING = 0.25; // Distance between lines

    /**
     * Creates a new single-line hologram instance.
     *
     * @param id Unique identifier for this hologram
     * @param location Base location where the hologram should appear
     * @param text Initial text to display (supports ChatColor codes like &c, &l, etc.)
     * @param heightOffset Vertical offset from the base location
     * @param visible Whether the hologram should be visible initially
     */
    public Hologram(String id, Location location, String text, double heightOffset, boolean visible) {
        this.id = id;
        this.location = location.clone();
        this.currentText = parseAndValidateText(text); // Apply formatting to initial text
        this.heightOffset = heightOffset;
        this.visible = visible;
        this.armorStands = new ArrayList<>();
        this.currentLines = new ArrayList<>();

        createArmorStand();
    }

    /**
     * Creates a new multi-line hologram instance.
     *
     * @param id Unique identifier for this hologram
     * @param location Base location where the hologram should appear
     * @param lines List of text lines to display (supports ChatColor codes like &c, &l, etc.)
     * @param heightOffset Vertical offset from the base location
     * @param visible Whether the hologram should be visible initially
     */
    public Hologram(String id, Location location, List<String> lines, double heightOffset, boolean visible) {
        this.id = id;
        this.location = location.clone();
        this.heightOffset = heightOffset;
        this.visible = visible;
        this.armorStands = new ArrayList<>();
        this.currentLines = new ArrayList<>();

        // Process and validate each line
        for (String line : lines) {
            this.currentLines.add(parseAndValidateText(line));
        }

        // Set currentText to joined lines for compatibility
        this.currentText = String.join("\n", this.currentLines);

        createMultiLineArmorStands();
    }

    /**
     * Validates that a location is suitable for hologram creation.
     *
     * @param location The location to validate
     * @throws IllegalArgumentException if the location is invalid
     */
    private void validateLocation(Location location) {
        if (location == null) {
            throw new IllegalArgumentException("Location cannot be null");
        }

        if (location.getWorld() == null) {
            throw new IllegalArgumentException("Location world cannot be null");
        }

        if (!location.getWorld().isChunkLoaded(location.getBlockX() >> 4, location.getBlockZ() >> 4)) {
            throw new IllegalArgumentException("Location chunk is not loaded: " +
                    formatLocationForError(location));
        }

        // Validate Y coordinate is within world bounds
        if (location.getY() < 0 || location.getY() > 256) {
            throw new IllegalArgumentException("Location Y coordinate out of bounds (0-256): " +
                    formatLocationForError(location));
        }
    }

    /**
     * Validates that an armor stand entity is still valid for operations.
     *
     * @param armorStand The armor stand to validate
     * @return true if the armor stand is valid, false otherwise
     */
    private boolean validateArmorStand(ArmorStand armorStand) {
        return armorStand != null && !armorStand.isDead() && armorStand.isValid();
    }

    /**
     * Formats a location for error messages.
     *
     * @param location The location to format
     * @return Formatted location string
     */
    private String formatLocationForError(Location location) {
        if (location == null) return "null";
        if (location.getWorld() == null) return "world=null";

        return String.format("%s(%.1f,%.1f,%.1f)",
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ());
    }

    /**
     * Creates and configures the armor stand entity for this hologram.
     */
    private void createArmorStand() {
        validateLocation(location);

        Location spawnLocation = location.clone().add(0, heightOffset, 0);

        try {
            armorStand = (ArmorStand) location.getWorld().spawnEntity(spawnLocation, EntityType.ARMOR_STAND);

            // Configure armor stand properties for hologram display
            configureArmorStand(armorStand, currentText);

            // Add to armor stands list for consistency
            armorStands.add(armorStand);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create armor stand for hologram '" + id + "': " + e.getMessage(), e);
        }
    }

    /**
     * Creates and configures multiple armor stands for multi-line hologram display.
     */
    private void createMultiLineArmorStands() {
        validateLocation(location);

        try {
            // Create armor stands from top to bottom
            for (int i = 0; i < currentLines.size(); i++) {
                double yOffset = heightOffset - (i * LINE_SPACING);
                Location spawnLocation = location.clone().add(0, yOffset, 0);

                ArmorStand lineArmorStand = (ArmorStand) location.getWorld().spawnEntity(spawnLocation, EntityType.ARMOR_STAND);
                configureArmorStand(lineArmorStand, currentLines.get(i));

                armorStands.add(lineArmorStand);

                // Set the first armor stand as the main one for compatibility
                if (i == 0) {
                    armorStand = lineArmorStand;
                }
            }
        } catch (Exception e) {
            // Clean up any armor stands that were created before the failure
            removeAllArmorStands();
            armorStands.clear();
            throw new RuntimeException("Failed to create multi-line armor stands for hologram '" + id + "': " + e.getMessage(), e);
        }
    }

    /**
     * Configures an armor stand with hologram properties.
     *
     * @param armorStand The armor stand to configure
     * @param text The text to display
     */
    private void configureArmorStand(ArmorStand armorStand, String text) {
        armorStand.setVisible(false);           // Invisible armor stand
        armorStand.setGravity(false);           // No gravity
        armorStand.setCustomNameVisible(visible); // Show/hide custom name
        armorStand.setCustomName(text);         // Display text
        armorStand.setCanPickupItems(false);    // No item pickup
        armorStand.setRemoveWhenFarAway(false); // Persistent
        armorStand.setBasePlate(false);         // No base plate
        armorStand.setArms(false);              // No arms
    }

    /**
     * Updates the hologram text with ChatColor formatting support.
     * Only updates if the text has changed to optimize performance.
     *
     * @param text New text to display (supports ChatColor codes like &c, &l, etc.)
     */
    public void setText(String text) {
        if (text == null) {
            text = "";
        }

        // Parse and validate text formatting
        String formattedText = parseAndValidateText(text);

        // Only update if text has actually changed
        if (!formattedText.equals(currentText)) {
            if (isValid()) {
                try {
                    // For single-line holograms, update the main armor stand
                    if (armorStands.size() == 1) {
                        if (validateArmorStand(armorStand)) {
                            armorStand.setCustomName(formattedText);
                            this.currentText = formattedText;
                        }
                    } else {
                        // For multi-line holograms, split text and update all lines
                        setLines(Arrays.asList(text.split("\n")));
                    }
                } catch (Exception e) {
                    // Graceful degradation - log error but don't crash
                    System.err.println("Failed to update hologram text for '" + id + "': " + e.getMessage());
                }
            }
        }
    }

    /**
     * Updates multi-line hologram text with ChatColor formatting support.
     *
     * @param lines List of text lines to display (supports ChatColor codes like &c, &l, etc.)
     */
    public void setLines(List<String> lines) {
        if (lines == null) {
            lines = new ArrayList<>();
        }

        // Parse and validate each line
        List<String> formattedLines = new ArrayList<>();
        for (String line : lines) {
            formattedLines.add(parseAndValidateText(line));
        }

        // Check if lines have changed
        if (!formattedLines.equals(currentLines)) {
            // Remove existing armor stands if line count changed
            if (formattedLines.size() != armorStands.size()) {
                // Validate location before recreating
                if (location.getWorld() == null || !location.getWorld().isChunkLoaded(location.getBlockX() >> 4, location.getBlockZ() >> 4)) {
                    return; // Don't recreate if chunk is not loaded
                }

                removeAllArmorStands();
                armorStands.clear();
                currentLines.clear();

                // Recreate with new line count
                this.currentLines.addAll(formattedLines);
                this.currentText = String.join("\n", this.currentLines);

                try {
                    createMultiLineArmorStands();
                } catch (Exception e) {
                    System.err.println("Failed to recreate multi-line armor stands for hologram '" + id + "': " + e.getMessage());
                    // Clear everything if recreation fails
                    armorStands.clear();
                    currentLines.clear();
                    return;
                }
            } else {
                // Update existing armor stands with new text
                for (int i = 0; i < formattedLines.size() && i < armorStands.size(); i++) {
                    ArmorStand lineArmorStand = armorStands.get(i);
                    if (lineArmorStand != null && !lineArmorStand.isDead() && lineArmorStand.isValid()) {
                        lineArmorStand.setCustomName(formattedLines.get(i));
                    }
                }
                this.currentLines = new ArrayList<>(formattedLines);
                this.currentText = String.join("\n", this.currentLines);
            }
        }
    }

    /**
     * Parses ChatColor codes and validates text formatting.
     * Handles invalid formatting gracefully without crashing.
     *
     * @param text Raw text with potential formatting codes
     * @return Formatted text with ChatColor codes applied
     */
    private String parseAndValidateText(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        try {
            // Translate color codes (& to §)
            String formattedText = ChatColor.translateAlternateColorCodes('&', text);

            // Validate that the formatted text isn't too long (Minecraft limit is 64 characters for custom names)
            if (formattedText.length() > 64) {
                // Truncate and add warning indicator
                formattedText = formattedText.substring(0, 61) + "...";
            }

            return formattedText;

        } catch (Exception e) {
            // Handle any formatting errors gracefully
            // Return the original text without formatting if parsing fails
            return text.length() > 64 ? text.substring(0, 61) + "..." : text;
        }
    }

    /**
     * Sets the visibility of the hologram.
     *
     * @param visible Whether the hologram should be visible
     */
    public void setVisible(boolean visible) {
        if (this.visible != visible) {
            this.visible = visible;
            try {
                // Update visibility for all armor stands with validation
                for (ArmorStand stand : armorStands) {
                    if (validateArmorStand(stand)) {
                        stand.setCustomNameVisible(visible);
                    }
                }
            } catch (Exception e) {
                // Graceful degradation - log error but don't crash
                System.err.println("Failed to update hologram visibility for '" + id + "': " + e.getMessage());
            }
        }
    }

    /**
     * Removes the hologram by despawning all armor stands.
     */
    public void remove() {
        removeAllArmorStands();
        armorStands.clear();
        currentLines.clear();
        armorStand = null;
    }

    /**
     * Removes all armor stands associated with this hologram.
     */
    private void removeAllArmorStands() {
        for (ArmorStand stand : armorStands) {
            if (stand != null && !stand.isDead() && stand.isValid()) {
                try {
                    stand.remove();
                } catch (Exception e) {
                    // Log but don't crash if removal fails
                    System.err.println("Failed to remove armor stand for hologram '" + id + "': " + e.getMessage());
                }
            }
        }
        // Add a small delay to ensure removal is processed
        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Checks if the hologram is valid (at least one armor stand exists and is not dead).
     *
     * @return true if the hologram is valid, false otherwise
     */
    public boolean isValid() {
        if (armorStands.isEmpty()) {
            return false;
        }

        // Check if at least one armor stand is valid
        for (ArmorStand stand : armorStands) {
            if (stand != null && !stand.isDead() && stand.isValid()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Gets the unique identifier for this hologram.
     *
     * @return The hologram ID
     */
    public String getId() {
        return id;
    }

    /**
     * Gets the base location of this hologram.
     *
     * @return A clone of the hologram location
     */
    public Location getLocation() {
        return location.clone();
    }

    /**
     * Gets the current text displayed by this hologram.
     *
     * @return The current text
     */
    public String getCurrentText() {
        return currentText;
    }

    /**
     * Gets the current lines displayed by this hologram.
     *
     * @return List of current lines (empty list for single-line holograms)
     */
    public List<String> getCurrentLines() {
        return new ArrayList<>(currentLines);
    }

    /**
     * Checks if this hologram is multi-line.
     *
     * @return true if hologram has multiple lines, false otherwise
     */
    public boolean isMultiLine() {
        return armorStands.size() > 1;
    }

    /**
     * Gets the number of lines in this hologram.
     *
     * @return Number of lines
     */
    public int getLineCount() {
        return armorStands.size();
    }

    /**
     * Gets whether this hologram is currently visible.
     *
     * @return true if visible, false otherwise
     */
    public boolean isVisible() {
        return visible;
    }

    /**
     * Gets the height offset from the base location.
     *
     * @return The height offset in blocks
     */
    public double getHeightOffset() {
        return heightOffset;
    }

    /**
     * Gets the underlying armor stand entity (for advanced operations).
     *
     * @return The armor stand entity, or null if invalid
     */
    public ArmorStand getArmorStand() {
        return isValid() ? armorStand : null;
    }
}