package me.miguel19877.dev.commands;

import me.miguel19877.dev.Economy;
import me.miguel19877.dev.managers.LanguageManager;
import me.miguel19877.dev.permissions.Permissions;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

public class Money implements CommandExecutor {

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!command.getName().equalsIgnoreCase("money")) {
            return true;
        }

        if (args.length == 0 && sender instanceof Player) {
            handleCheckBalance((Player) sender);
            return true;
        }

        if (args.length == 1) {
            return handleSingleArgCommands(sender, args);
        } else if (args.length == 2) {
            handleAdminCommands(sender, args);
            return true;
        } else if (args.length == 3) {
            handleTransaction(sender, args[0].toLowerCase(), args[1], args[2]);
            return true;
        }

        return true;
    }

    private boolean handleSingleArgCommands(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            LanguageManager.getInstance().sendMessage((Player) sender, "money.invalid");
            return true;
        }
        Player player = (Player) sender;
        if (args[0].equalsIgnoreCase("ajuda") || args[0].equalsIgnoreCase("help")) {
            showHelp(player);
        } else {
            handleCheckOtherPlayerBalance(player, args[0]);
        }
        return true;
    }

    private void handleAdminCommands(CommandSender sender, String[] args) {
        LanguageManager langManager = LanguageManager.getInstance();
        Player player = (Player) sender;

        if (args[0].equalsIgnoreCase("setar") || args[0].equalsIgnoreCase("add") || args[0].equalsIgnoreCase("remove")) {
            if (Permissions.getGrupoId(player) < 12) {
                langManager.sendMessage(player, "money.no_permission");
                return;
            }
            if (args[0].equalsIgnoreCase("setar")) {
                langManager.sendMessage(player, "money.usage_set");
            } else if (args[0].equalsIgnoreCase("add")) {
                langManager.sendMessage(player, "money.usage_add");
            } else {
                langManager.sendMessage(player, "money.usage_remove");
            }
        } else if (args[0].equalsIgnoreCase("enviar")){
            langManager.sendMessage(player, "money.usage_send");
        }else {
            langManager.sendMessage(player, "money.unknown_command");
        }
    }

    private void handleCheckBalance(Player player) {
        long money = Economy.getMoney(player.getUniqueId());
        LanguageManager.getInstance().sendMessage(player, "money.balance", String.valueOf(money));
    }

    private void handleCheckOtherPlayerBalance(Player player, String playerName) {
        Player target = Bukkit.getPlayerExact(playerName);
        if (target == null) {
            LanguageManager.getInstance().sendMessage(player, "money.player_not_found");
            return;
        }
        long money = Economy.getMoney(target.getUniqueId());
        LanguageManager.getInstance().sendMessage(player, "money.other_balance", playerName, String.valueOf(money));
    }

    private void handleTransaction(CommandSender sender, String commandType, String playerName, String amountStr) {
        LanguageManager langManager = LanguageManager.getInstance();
        UUID senderUUID = null;

        if (sender instanceof Player) {
            senderUUID = ((Player) sender).getUniqueId();
        }

        if (!isInt(amountStr)) {
            if (sender instanceof Player) {
                langManager.sendMessage((Player) sender, "money.invalid_number");
            } else {
                sender.sendMessage("Invalid number format");
            }
            return;
        }

        Long amount = Long.parseLong(amountStr);
        OfflinePlayer target = Bukkit.getOfflinePlayer(UUID.nameUUIDFromBytes(("OfflinePlayer:" + playerName).getBytes()));
        if (target == null) {
            if (sender instanceof Player) {
                langManager.sendMessage((Player) sender, "money.player_not_found");
            } else {
                sender.sendMessage("Player not found");
            }
            return;
        }

        switch (commandType) {
            case "setar":
                if (sender instanceof Player && Permissions.getGrupoId((Player) sender) < 12) {
                    langManager.sendMessage((Player) sender, "money.no_permission");
                    return;
                }
                Economy.setMoney(target.getUniqueId(), amount);
                break;
            case "add":
                if (sender instanceof Player && Permissions.getGrupoId((Player) sender) < 12) {
                    langManager.sendMessage((Player) sender, "money.no_permission");
                    return;
                }
                Economy.addMoney(target.getUniqueId(), amount);
                break;
            case "remove":
                if (sender instanceof Player && Permissions.getGrupoId((Player) sender) < 12) {
                    langManager.sendMessage((Player) sender, "money.no_permission");
                    return;
                }
                Economy.removeMoney(target.getUniqueId(), amount);
                break;
            case "enviar":
                if (!(sender instanceof Player)) {
                    sender.sendMessage("This command can only be used by players");
                    return;
                }
                if (Economy.getMoney(senderUUID) < amount) {
                    langManager.sendMessage((Player) sender, "money.insufficient_funds");
                    return;
                }
                Economy.transferMoney(senderUUID, target.getUniqueId(), amount);
                break;
            default:
                if (sender instanceof Player) {
                    langManager.sendMessage((Player) sender, "money.unknown_command");
                } else {
                    sender.sendMessage("Unknown command");
                }
                return;
        }
        if (sender instanceof Player) {
            langManager.sendMessage((Player) sender, "money.transaction_success", playerName, String.valueOf(Economy.getMoney(target.getUniqueId())));
        } else {
            sender.sendMessage("Transaction successful for " + playerName + ". New balance: " + Economy.getMoney(target.getUniqueId()));
        }
    }

    private void showHelp(Player player) {
        LanguageManager langManager = LanguageManager.getInstance();
        langManager.sendMessage(player, "money.help.header");
        langManager.sendMessage(player, "money.help.balance");
        langManager.sendMessage(player, "money.help.other_balance");
        langManager.sendMessage(player, "money.help.send");
        langManager.sendMessage(player, "money.help.set");
        langManager.sendMessage(player, "money.help.add");
        langManager.sendMessage(player, "money.help.remove");
    }

    public boolean isInt(String s) {
        try {
            Long.parseLong(s);
            return true;
        } catch (NumberFormatException ex) {
            return false;
        }
    }
}
